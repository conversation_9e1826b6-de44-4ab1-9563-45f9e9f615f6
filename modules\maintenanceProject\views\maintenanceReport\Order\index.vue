<template>
    <div class="demand-container">
        <Header ref="headerRef" @get-table-list="getTableData" pageType="order">
            <template #customButton>
                <el-button
                    v-show="
                        projectStore.length === 3 &&
                        (isProjectManager || isPqa) &&
                        isEditable
                    "
                    type="text"
                    @click="handleSelectOrder"
                    class="select-order-button"
                >
                    选择订单
                </el-button>
            </template>
        </Header>
        <el-table
            :data="tableData"
            style="width: 100%"
            :cell-style="{ verticalAlign: 'top' }"
            height="calc(100vh - 180px)"
        >
            <el-table-column
                prop="customerName"
                label="客户"
                header-align="center"
                align="left"
            ></el-table-column>
            <el-table-column
                prop="productModel"
                label="型号及数量"
                header-align="center"
                align="center"
                width="160"
            >
                <template slot-scope="scope">
                    <div class="product-model-container">
                        <div class="model-name">
                            {{ scope.row.productModel }}
                        </div>
                        <div class="quantity-list">
                            <el-tooltip
                                v-for="(qty, index) in scope.row
                                    .orderRequireCountList"
                                :key="index"
                                effect="dark"
                                :content="qty.baseConfigRequire"
                                placement="top"
                                popper-class="maintenanceReport-multiline-tooltip"
                                :disabled="!qty.baseConfigRequire"
                            >
                                <span class="quantity-item">
                                    {{ qty.orderCount }}
                                </span>
                            </el-tooltip>
                        </div>
                        <div class="project-manager">
                            <div class="project-manager-container">
                                项目经理
                            </div>
                            <div>{{ scope.row.projectManagerName }}</div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="日期" width="110" header-align="center">
                <template slot-scope="scope">
                    <div class="deadline-container">
                        <div>订单发起日期</div>
                        <div style="margin-bottom: 10px">
                            {{ scope.row.orderApplyDate }}
                        </div>
                        <div>计划完成日期</div>
                        <div>{{ scope.row.planFinishDate }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="响应计划及进展"
                min-width="300"
                header-align="center"
            >
                <template slot-scope="scope">
                    <div class="response-plan-container">
                        <div
                            v-for="(config, configIndex) in scope.row
                                .planProgressList"
                            :key="configIndex"
                        >
                            <div
                                class="config-title"
                                v-if="
                                    !(
                                        scope.row.planProgressList.length ===
                                            1 && config.taskGroup === '其他'
                                    )
                                "
                            >
                                【{{ config.taskGroup }}】
                            </div>
                            <div
                                :key="`${configIndex}-items`"
                                class="config-items"
                            >
                                <div
                                    v-for="(
                                        item, itemIndex
                                    ) in config.taskDetailList"
                                    :key="`${configIndex}-${itemIndex}`"
                                    class="plan-item"
                                >
                                    <div
                                        :class="[
                                            'plan-item-content',
                                            {
                                                clickable: isClickable(item)
                                            }
                                        ]"
                                        @click="
                                            isClickable(item)
                                                ? handlePlanItemClick(item)
                                                : null
                                        "
                                    >
                                        ● {{ item.deadline }}，{{
                                            item.taskName
                                        }}（{{ item.responsiblePerson }}），{{
                                            item.taskProgress
                                                ? `${item.taskProgress}%`
                                                : '未启动'
                                        }}
                                    </div>
                                    <div v-if="item.taskNote" class="taskNote">
                                        进展补充：{{ item.taskNote }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="风险及需支持事项"
                min-width="300"
                header-align="center"
            >
                <template slot-scope="scope">
                    <div
                        v-if="
                            scope.row.riskList && scope.row.riskList.length > 0
                        "
                    >
                        <div
                            v-for="(item, index) in scope.row.riskList"
                            :key="index"
                            class="riskDesc-support-container"
                        >
                            <div v-if="item.riskDesc">
                                <div class="section-title">
                                    【风险描述及影响】：
                                </div>
                                <div class="riskDesc-item">
                                    {{ item.riskDesc }}
                                </div>
                            </div>
                            <div
                                v-if="
                                    item.riskSupportList &&
                                    item.riskSupportList.length > 0
                                "
                            >
                                <div class="section-title">
                                    【需支持事项】：
                                </div>
                                <div
                                    v-for="(
                                        support, supportIndex
                                    ) in item.riskSupportList"
                                    :key="`support-${index}-${supportIndex}`"
                                    class="support-item"
                                >
                                    ● {{ support.expectedDate }}，{{
                                        support.supportItem
                                    }}[{{ support.responsibleOrg }}]
                                </div>
                            </div>
                            <div
                                v-else-if="
                                    item.riskSupportList &&
                                    item.riskSupportList.length === 0
                                "
                            >
                                <div class="section-title">
                                    【需支持事项】：
                                </div>
                                <div class="support-item">● 无</div>
                            </div>
                        </div>
                    </div>
                    <div v-else>无风险</div>
                </template>
            </el-table-column>
            <el-table-column
                prop="orderStatus"
                label="状态"
                header-align="center"
                align="center"
                width="80"
            ></el-table-column>
        </el-table>
        <SupplementDialog
            :visible.sync="supplementDialogVisible"
            :supplement="supplement"
            :id="taskId"
            @success="getTableData"
        ></SupplementDialog>
        <SelectOrderDialog
            :visible.sync="selectOrderDialogVisible"
            @success="handleSelectSuccess"
        ></SelectOrderDialog>
    </div>
</template>

<script>
import Header from '../Header';
import SupplementDialog from 'maintenanceProject/views/maintenanceReport/components/SupplementDialog';
import SelectOrderDialog from './SelectOrderDialog';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'Order',
    components: {
        Header,
        SupplementDialog,
        SelectOrderDialog
    },
    props: {},
    data() {
        return {
            tableData: [],
            supplement: '',
            options: {
                weekReportIdGetList: []
            },
            // 每条任务的ID
            taskId: '',
            supplementDialogVisible: false,
            selectOrderDialogVisible: false
        };
    },
    computed: {
        // 维护项目信息：产品线/细分产品线/项目经理
        projectStore() {
            return (
                this.$store.state.maintenanceProject.maintenanceProjectStore ||
                []
            );
        },
        // 周报ID
        weeklyId() {
            return this.$store.state.maintenanceProject.maintenanceReportHeader
                .weeklyId;
        },
        // 当前选中的周报选项（从store获取）
        currentSelectedOption() {
            return this.$store.state.maintenanceProject.currentSelectedOption;
        },
        // 是否是项目经理
        isProjectManager() {
            // 不选到项目经理，无法编辑
            if (this.projectStore.length !== 3) return false;
            return getUserAccount(this) === this.projectStore[2];
        },
        isPqa() {
            return (
                this.$store.state.maintenanceProject.maintenanceReportHeader
                    .isPqa || false
            );
        },
        // 是否能够编辑，待更新才能编辑
        isEditable() {
            return (
                this.$store.state.maintenanceProject.maintenanceReportHeader
                    .weeklyStatus === '待更新'
            );
        }
    },
    methods: {
        /**
         * 弹窗保存成功之后的处理
         * 调用Header组件的handleSelectChange方法来刷新数据
         */
        handleSelectSuccess() {
            // 调用Header组件的handleSelectChange方法
            if (
                this.$refs.headerRef &&
                this.$refs.headerRef.handleProjectChange
            ) {
                this.$refs.headerRef.handleProjectChange();
            }
        },
        /**
         * 处理选择订单按钮点击事件
         * 打开选择订单对话框
         */
        handleSelectOrder() {
            this.selectOrderDialogVisible = true;
        },
        /**
         * 获取表格数据
         * 使用store中的当前选中周报选项
         */
        async getTableData() {
            if (this.currentSelectedOption.weekReportIdGetList.length === 0) {
                this.tableData = [];
                return;
            }
            const api =
                this.$service.maintenanceProject.weekly.getOrderListByWeeklyId;
            const params = this.currentSelectedOption.weekReportIdGetList;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.tableData = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 处理计划项点击事件
         * 打开补充信息对话框
         * @param {Object} item - 被点击的计划项数据
         */
        handlePlanItemClick(item) {
            this.supplementDialogVisible = true;
            this.supplement = item.taskNote;
            this.taskId = item.id;
        },
        /**
         * 判断计划项是否可点击
         * @param {Object} item - 计划项数据
         * @returns {boolean} 是否可点击
         */
        isClickable(item) {
            if (!this.isEditable) return false;
            // 不选择到项目经理（三级），无法编辑
            if (this.projectStore.length !== 3) return false;
            // 如果是任务对应的责任人/项目经理/PQA，就能编辑
            return (
                getUserAccount(this) === item.responsiblePersonAccount ||
                this.isProjectManager ||
                this.isPqa
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.deadline-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    div:nth-child(odd) {
        font-weight: bold;
    }
}

.response-plan-container {
    .config-title {
        font-weight: bold;
        // 非第一个配置标题，添加10px的间距
        &:not(:first-child) {
            margin-top: 10px;
        }
    }

    .config-items {
        padding-left: 15px;
        margin-bottom: 10px;

        .plan-item {
            margin: 5px 0;
            padding-left: 15px;
        }
        .plan-item-content {
            &.clickable {
                cursor: pointer;
                &:hover {
                    color: #409eff;
                    background: #f5f7fa;
                }
            }
        }
        .taskNote {
            white-space: pre-line;
            margin-top: 5px;
            margin-left: 15px;
            color: #606266;
            font-style: italic;
            background-color: #ffffcc;
        }
    }
}

.riskDesc-support-container {
    // 非第一个标题，添加10px的间距
    &:not(:first-child) {
        margin-top: 20px;
    }
    .section-title {
        font-weight: bold;
    }

    .riskDesc-item,
    .support-item {
        margin: 5px 0;
        padding-left: 15px;
    }
}

.select-order-button {
    margin-right: 15px;
}
.demand-name-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.product-model-container {
    display: flex;
    flex-direction: column;
    padding: 10px;

    .model-name {
        font-weight: bold;
        margin-bottom: 8px;
    }

    .quantity-list {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 4px;
        margin-bottom: 8px;

        .quantity-item {
            background-color: #99999926;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
            display: inline-block;
            cursor: pointer;
        }
    }
    .project-manager-container {
        font-weight: bold;
    }
}
</style>
<style lang="scss">
.el-tooltip__popper.maintenanceReport-multiline-tooltip {
    max-width: 300px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    line-height: 1.4;
    text-align: left;
}
</style>
